# 微信消息处理测试样本

## 测试场景1：重复价格问题

### 样本1.1：同公司同作物多价格（合并优先级规则）

**输入：**

```
山东青援食品有限公司:自2025年7月7日8:20起，白麦普麦老厂基础价2.45元/kg，白麦优麦新厂最高价2.47元/kg，红麦普麦老厂基础价2.40元/kg。
```

**期望输出：**

- 只有一条记录
- 公司：山东青援食品有限公司
- 作物：小麦
- 价格：1.225元/斤（白麦普麦老厂基础价：4+3+2=9分，权重最高）

### 样本1.2：水分条件优先级测试

**输入：**

```
金龙面业今日小麦收购价：≤14%水分2.45元/kg，>14%水分2.40元/kg，≤30%水分潮粮2.35元/kg，>30%水分潮粮2.30元/kg。
```

**期望输出：**

- 只有一条记录
- 公司：金龙面业
- 作物：小麦
- 价格：1.225元/斤（≤14%水分：5分，权重最高）

### 样本1.3：多公司多作物

**输入：**

```
今日收购价格：金龙面业小麦1.45元/斤，玉米1.25元/斤；五得利面粉厂小麦1.48元/斤，大豆2.30元/斤。
```

**期望输出：**

- 四条记录，每个公司-作物组合一条
- 无重复的company+crop组合

## 测试场景2：豆粕转换问题

### 样本2.1：豆粕价格处理

**输入：**

```
华鲁恒升今日豆粕报价：43%蛋白2800元/吨，46%蛋白2950元/吨，散装2750元/吨。
```

**期望输出：**

- 只有一条记录
- 公司：华鲁恒升
- 作物：豆粕
- 价格：2950元/吨（保持原单位，选择最高品质）

### 样本2.2：混合农作物和工业产品

**输入：**

```
山东某公司今日报价：小麦2.40元/kg，玉米2.20元/kg，豆粕2900元/吨，菜粕2600元/吨。
```

**期望输出：**

- 四条记录
- 小麦、玉米转换为元/斤
- 豆粕、菜粕保持元/吨

## 测试场景3：信息提取准确性

### 样本3.1：复杂格式消息

**输入：**

```
【收购信息】
公司：昌乐英轩实业有限公司
时间：2025年7月27日
品种及价格：
- 新小麦（一等）：1.46元/斤
- 玉米（标准水分）：1.28元/斤
- 大豆（进口）：2.35元/斤
备注：以上价格含税带票
```

**期望输出：**

- 三条记录
- 正确提取公司名称、作物类型、价格
- 日期格式标准化

### 样本3.2：边缘情况处理

**输入：**

```
某面粉厂小麦面议，玉米1.25，大豆价格待定。五得利小麦1.48元/斤。
```

**期望输出：**

- 只有一条记录（五得利小麦）
- 忽略不完整信息

## 测试场景4：单位转换验证

### 样本4.1：多种单位混合

**输入：**

```
今日粮价：小麦2.45元/公斤，玉米2200元/吨，大豆4.60元/kg，花生6.80元/斤。
```

**期望输出：**

- 四条记录
- 所有价格统一转换为元/斤
- 转换计算正确

### 样本4.2：特殊单位处理

**输入：**

```
价格更新：小麦245分/斤，玉米1.3元/500g，大豆23角/斤。
```

**期望输出：**

- 三条记录
- 分、角正确转换为元
- 500g转换为斤

## 测试场景5：日期处理

### 样本5.1：明确日期

**输入：**

```
2025年7月25日价格：金龙面业小麦1.45元/斤。
```

**期望输出：**

- 日期：2025-07-25

### 样本5.2：相对日期

**输入：**

```
今日价格：五得利小麦1.48元/斤。
```

**期望输出：**

- 日期：2025-07-27（当前日期）

## 成功标准验证点

1. **去重验证**：每个company+crop组合只有一条记录
2. **豆粕处理**：工业产品保持原单位，传统农作物转换单位
3. **准确性验证**：正确提取所有关键信息
4. **格式验证**：输出标准JSON格式
5. **边界处理**：正确处理不完整或异常数据
