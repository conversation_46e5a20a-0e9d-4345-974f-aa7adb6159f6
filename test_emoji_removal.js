/**
 * 测试优化后的 removeEmojisForAI 函数
 * 验证是否能正确移除各种类型的表情符号和特殊字符
 */

import { removeEmojisForAI } from './src/utils/messageUtils.js'

// 测试用例
const testCases = [
  {
    name: '基本表情符号',
    input: '今天天气很好😀😃😄😁😆😅😂🤣😊😇',
    expected: '今天天气很好'
  },
  {
    name: '动物表情符号',
    input: '我喜欢小动物🐶🐱🐭🐹🐰🦊🐻🐼🐨🐯',
    expected: '我喜欢小动物'
  },
  {
    name: '食物表情符号',
    input: '晚餐吃什么🍎🍌🍇🍓🥝🍅🥑🥕🌽🥒',
    expected: '晚餐吃什么'
  },
  {
    name: '交通工具表情符号',
    input: '出行方式🚗🚕🚙🚌🚎🏎️🚓🚑🚒🚐',
    expected: '出行方式'
  },
  {
    name: '符号和箭头',
    input: '方向指示→←↑↓↗↖↘↙⬆⬇⬅➡',
    expected: '方向指示'
  },
  {
    name: '几何形状',
    input: '形状测试■□▲△●○◆◇★☆',
    expected: '形状测试'
  },
  {
    name: '扑克牌符号',
    input: '扑克游戏🂡🂢🂣🂤🂥🂦🂧🂨🂩🂪🂫',
    expected: '扑克游戏'
  },
  {
    name: '国旗表情符号',
    input: '各国国旗🇨🇳🇺🇸🇯🇵🇰🇷🇬🇧🇫🇷🇩🇪🇮🇹',
    expected: '各国国旗'
  },
  {
    name: '混合内容',
    input: '今天😀去公园🏞️看花🌸，坐地铁🚇回家🏠，很开心😊！',
    expected: '今天去公园看花，坐地铁回家，很开心！'
  },
  {
    name: '零宽字符和控制字符',
    input: '测试\u200B零宽\u200C字符\u200D和\uFEFF控制字符',
    expected: '测试零宽字符和控制字符'
  },
  {
    name: 'CJK符号',
    input: '中文符号测试〰️〽️㊗️㊙️🈁🈂🈚🈯🈲🈳',
    expected: '中文符号测试'
  },
  {
    name: '技术符号',
    input: '技术符号⌚⌛⏰⏱️⏲️⏳⌨️🖥️🖨️🖱️',
    expected: '技术符号'
  },
  {
    name: '空字符串',
    input: '',
    expected: ''
  },
  {
    name: '纯文本',
    input: '这是一段纯文本，没有任何表情符号。',
    expected: '这是一段纯文本，没有任何表情符号。'
  },
  {
    name: '多余空白字符',
    input: '  多余的   空白   字符  ',
    expected: '多余的 空白 字符'
  }
]

// 运行测试
console.log('🧪 开始测试优化后的 removeEmojisForAI 函数...\n')

let passedTests = 0
let totalTests = testCases.length

testCases.forEach((testCase, index) => {
  const result = removeEmojisForAI(testCase.input)
  const passed = result === testCase.expected
  
  console.log(`测试 ${index + 1}: ${testCase.name}`)
  console.log(`输入: "${testCase.input}"`)
  console.log(`期望: "${testCase.expected}"`)
  console.log(`结果: "${result}"`)
  console.log(`状态: ${passed ? '✅ 通过' : '❌ 失败'}`)
  
  if (passed) {
    passedTests++
  } else {
    console.log(`❌ 不匹配！`)
  }
  
  console.log('---')
})

console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`)
console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

if (passedTests === totalTests) {
  console.log('🎉 所有测试都通过了！')
} else {
  console.log('⚠️ 有测试失败，请检查实现。')
}
