/**
 * 提示词优化效果测试脚本
 * 用于验证优化后的提示词在各种场景下的表现
 */

import { processTextWithAi } from './src/services/aiService.js'
import { initializeAiService } from './src/services/aiService.js'
import { loadAllConfigs } from './src/config/configLoader.js'
import { log } from './src/utils/logging.js'
import ResultValidator from './src/utils/resultValidator.js'

// 测试样本
const TEST_SAMPLES = [
  {
    name: '重复价格场景1：同公司同作物多价格（合并优先级规则）',
    input:
      '山东青援食品有限公司:自2025年7月7日8:20起，白麦普麦老厂基础价2.45元/kg，白麦优麦新厂最高价2.47元/kg，红麦普麦老厂基础价2.40元/kg。',
    expectedCount: 1,
    expectedCompany: '山东青援食品有限公司',
    expectedCrop: '小麦',
    expectedPriceRange: [1.22, 1.23],
    description: '应该只输出一条记录，选择白麦普麦老厂基础价（权重9分最高）',
  },
  {
    name: '重复价格场景2：水分条件优先级测试',
    input:
      '金龙面业今日小麦收购价：≤14%水分2.45元/kg，>14%水分2.40元/kg，≤30%水分潮粮2.35元/kg，>30%水分潮粮2.30元/kg。',
    expectedCount: 1,
    expectedCompany: '金龙面业',
    expectedCrop: '小麦',
    expectedPriceRange: [1.22, 1.23],
    description: '应该只输出一条记录，选择≤14%水分（权重5分最高）',
  },
  {
    name: '重复价格场景3：多公司多作物',
    input:
      '今日收购价格：金龙面业小麦1.45元/斤，玉米1.25元/斤；五得利面粉厂小麦1.48元/斤，大豆2.30元/斤。',
    expectedCount: 4,
    description: '应该输出四条记录，每个公司-作物组合一条',
  },
  {
    name: '豆粕处理场景1：豆粕价格处理',
    input: '华鲁恒升今日豆粕报价：43%蛋白2800元/吨，46%蛋白2950元/吨，散装2750元/吨。',
    expectedCount: 1,
    expectedCompany: '华鲁恒升',
    expectedCrop: '豆粕',
    expectedUnit: '元/吨',
    expectedPrice: 2950,
    description: '应该只输出一条记录，保持原单位，选择最高品质',
  },
  {
    name: '豆粕处理场景2：混合农作物和工业产品',
    input: '山东某公司今日报价：小麦2.40元/kg，玉米2.20元/kg，豆粕2900元/吨，菜粕2600元/吨。',
    expectedCount: 4,
    description: '小麦、玉米转换为元/斤，豆粕、菜粕保持元/吨',
  },
  {
    name: '信息提取准确性场景：复杂格式消息',
    input: `【收购信息】
公司：昌乐英轩实业有限公司
时间：2025年7月27日
品种及价格：
- 新小麦（一等）：1.46元/斤
- 玉米（标准水分）：1.28元/斤
- 大豆（进口）：2.35元/斤
备注：以上价格含税带票`,
    expectedCount: 3,
    expectedCompany: '昌乐英轩实业有限公司',
    description: '应该正确提取三条记录，公司名称、作物类型、价格准确',
  },
]

/**
 * 验证测试结果
 */
function validateTestResult(result, expected, testName) {
  const issues = []

  // 检查记录数量
  if (result.length !== expected.expectedCount) {
    issues.push(`记录数量不符：期望${expected.expectedCount}条，实际${result.length}条`)
  }

  // 检查唯一性
  const uniqueKeys = new Set()
  const duplicates = []

  result.forEach(item => {
    const key = `${item.company}|${item.crop}`
    if (uniqueKeys.has(key)) {
      duplicates.push(key)
    }
    uniqueKeys.add(key)
  })

  if (duplicates.length > 0) {
    issues.push(`发现重复记录：${duplicates.join(', ')}`)
  }

  // 检查特定期望值
  if (expected.expectedCompany) {
    const hasExpectedCompany = result.some(item => item.company === expected.expectedCompany)
    if (!hasExpectedCompany) {
      issues.push(`未找到期望的公司：${expected.expectedCompany}`)
    }
  }

  if (expected.expectedCrop) {
    const hasExpectedCrop = result.some(item => item.crop === expected.expectedCrop)
    if (!hasExpectedCrop) {
      issues.push(`未找到期望的作物：${expected.expectedCrop}`)
    }
  }

  if (expected.expectedUnit) {
    const hasExpectedUnit = result.some(item => item.unit === expected.expectedUnit)
    if (!hasExpectedUnit) {
      issues.push(`未找到期望的单位：${expected.expectedUnit}`)
    }
  }

  if (expected.expectedPrice) {
    const hasExpectedPrice = result.some(item => item.price === expected.expectedPrice)
    if (!hasExpectedPrice) {
      issues.push(`未找到期望的价格：${expected.expectedPrice}`)
    }
  }

  if (expected.expectedPriceRange) {
    const [min, max] = expected.expectedPriceRange
    const pricesInRange = result.filter(item => item.price >= min && item.price <= max)
    if (pricesInRange.length === 0) {
      issues.push(`价格不在期望范围内：${min}-${max}`)
    }
  }

  return {
    passed: issues.length === 0,
    issues,
    testName,
    resultCount: result.length,
    expectedCount: expected.expectedCount,
  }
}

/**
 * 运行单个测试
 */
async function runSingleTest(testSample) {
  console.log(`\n🧪 测试：${testSample.name}`)
  console.log(`📝 描述：${testSample.description}`)
  console.log(
    `📄 输入：${testSample.input.substring(0, 100)}${testSample.input.length > 100 ? '...' : ''}`,
  )

  try {
    const result = await processTextWithAi(testSample.input)
    console.log(`📊 AI输出：${result.length}条记录`)

    // 显示结果详情
    result.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.company} - ${item.crop}: ${item.price}${item.unit}`)
    })

    // 验证结果
    const validation = validateTestResult(result, testSample, testSample.name)

    if (validation.passed) {
      console.log(`✅ 测试通过`)
    } else {
      console.log(`❌ 测试失败：`)
      validation.issues.forEach(issue => {
        console.log(`   - ${issue}`)
      })
    }

    return validation
  } catch (error) {
    console.log(`💥 测试异常：${error.message}`)
    return {
      passed: false,
      issues: [`测试异常：${error.message}`],
      testName: testSample.name,
      resultCount: 0,
      expectedCount: testSample.expectedCount,
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始提示词优化效果测试...\n')

  // 初始化服务
  console.log('⚙️ 初始化AI服务...')
  const configs = await loadAllConfigs()
  const initialized = await initializeAiService(
    configs.serverData,
    configs.prompts,
    configs.aliases,
  )

  if (!initialized) {
    console.log('❌ AI服务初始化失败')
    return
  }

  console.log('✅ AI服务初始化成功')

  // 运行测试
  const results = []
  for (const testSample of TEST_SAMPLES) {
    const result = await runSingleTest(testSample)
    results.push(result)
  }

  // 汇总结果
  console.log('\n📈 测试结果汇总：')
  const passedTests = results.filter(r => r.passed)
  const failedTests = results.filter(r => !r.passed)

  console.log(`✅ 通过：${passedTests.length}/${results.length}`)
  console.log(`❌ 失败：${failedTests.length}/${results.length}`)

  if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试：')
    failedTests.forEach(test => {
      console.log(`   - ${test.testName}`)
      test.issues.forEach(issue => {
        console.log(`     * ${issue}`)
      })
    })
  }

  // 成功标准验证
  console.log('\n🎯 成功标准验证：')
  const uniquenessIssues = results.filter(r => r.issues.some(issue => issue.includes('重复记录')))
  const countIssues = results.filter(r => r.resultCount !== r.expectedCount)

  console.log(`   去重验证：${uniquenessIssues.length === 0 ? '✅ 通过' : '❌ 失败'}`)
  console.log(`   数量验证：${countIssues.length === 0 ? '✅ 通过' : '❌ 失败'}`)
  console.log(`   整体通过率：${((passedTests.length / results.length) * 100).toFixed(1)}%`)

  return results
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error)
}

export { runAllTests, runSingleTest, validateTestResult }
