import fs from 'fs'
import path from 'path'
import config from '../config/index.js'
import { formatTimestamp } from './dateUtils.js'
import { ensureDirSync, fileExistsSync, createFileBox } from './fileUtils.js'
import { log } from './logging.js'

/**
 * 保存接收到的文件
 * @param {Object} agent WechatferryAgent实例
 * @param {Object} msg 消息对象
 * @param {string} saveDir 保存目录
 * @returns {Promise<string|null>} 保存的文件路径
 */
async function saveMessageFile(agent, msg, saveDir = config.paths.files) {
  try {
    if (!saveDir || typeof saveDir !== 'string') {
      throw new Error('无效的保存目录')
    }

    // 确保保存目录存在
    ensureDirSync(saveDir)

    if (!agent || typeof agent.downloadFile !== 'function') {
      throw new Error('无效的agent实例')
    }
    const fileBox = await agent.downloadFile(msg)
    const fileName = `${Date.now()}_${fileBox.name}`
    const savePath = path.join(saveDir, fileName)
    await fileBox.toFile(savePath)
    return savePath
  } catch (error) {
    log.c.error(`保存文件失败`, {
      error: error.message,
      stack: error.stack,
    })
    return null
  }
}

/**
 * 获取联系人名称
 * @param {Object} agent WechatferryAgent实例
 * @param {string} wxid 微信ID
 * @returns {string} 联系人名称
 */
function getContactName(agent, wxid) {
  try {
    if (!agent || typeof agent.getContactInfo !== 'function') {
      return wxid
    }
    const contact = agent.getContactInfo(wxid)
    return contact && contact.nickName ? contact.nickName : wxid
  } catch (error) {
    return wxid
  }
}

/**
 * 获取群聊名称
 * @param {Object} agent WechatferryAgent实例
 * @param {string} roomId 群聊ID
 * @returns {string} 群聊名称
 */
function getRoomName(agent, roomId) {
  try {
    if (!agent || typeof agent.getChatRoomInfo !== 'function') {
      return roomId
    }
    const room = agent.getChatRoomInfo(roomId)
    return room && room.nickName ? room.nickName : roomId
  } catch (error) {
    return roomId
  }
}

/**
 * 专门用于AI处理前清理表情符号和特殊字符的函数
 * @param {string} text 原始文本
 * @returns {string} 清理后的文本
 */
function removeEmojisForAI(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }

  return (
    text
      // 移除所有表情符号 (Emoji)
      .replace(/[\u{1F600}-\u{1F64F}]/gu, '') // 表情符号
      .replace(/[\u{1F300}-\u{1F5FF}]/gu, '') // 杂项符号和象形文字
      .replace(/[\u{1F680}-\u{1F6FF}]/gu, '') // 交通和地图符号
      .replace(/[\u{1F700}-\u{1F77F}]/gu, '') // 炼金术符号
      .replace(/[\u{1F780}-\u{1F7FF}]/gu, '') // 几何形状扩展
      .replace(/[\u{1F800}-\u{1F8FF}]/gu, '') // 补充箭头-C
      .replace(/[\u{1F900}-\u{1F9FF}]/gu, '') // 补充符号和象形文字
      .replace(/[\u{1FA00}-\u{1FA6F}]/gu, '') // 扩展-A
      .replace(/[\u{1FA70}-\u{1FAFF}]/gu, '') // 扩展-B
      .replace(/[\u{2600}-\u{26FF}]/gu, '') // 杂项符号
      .replace(/[\u{2700}-\u{27BF}]/gu, '') // 装饰符号
      .replace(/[\u{FE00}-\u{FE0F}]/gu, '') // 变体选择器
      .replace(/[\u{1F1E0}-\u{1F1FF}]/gu, '') // 区域指示符号（国旗）
      // 移除零宽字符
      .replace(/[\u200B-\u200D\uFEFF]/g, '')
      // 移除其他可能影响AI处理的特殊字符
      .replace(/[\u2000-\u206F]/g, '') // 通用标点
      .replace(/[\u2E00-\u2E7F]/g, '') // 补充标点
      // 清理控制字符（除了基本的换行符和制表符）
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // 清理可能导致问题的特殊字符
      .replace(/[\x00\x1A]/g, '')
      // 清理多余的空白字符，但保留基本的空格和换行
      .replace(/\s+/g, ' ')
      .trim()
  )
}

/**
 * 格式化消息对象
 * @param {Object} agent WechatferryAgent实例
 * @param {Object} msg 消息对象
 * @returns {Object} 格式化后的消息对象
 */
async function formatMessage(agent, msg) {
  const { id, type, is_group, content, sender, roomid, thumb, ts, filePath } = msg
  const contactName = getContactName(agent, sender)
  const formattedMsg = {
    messageId: id,
    msgType: type,
    content: removeEmojisForAI(content || ''),
    sender: contactName,
    senderId: sender,
    sendTime: formatTimestamp(ts, 'YYYY-MM-DD HH:mm:ss'),
    ...(is_group && {
      room: getRoomName(agent, roomid),
      roomId: roomid,
    }),
  }
  // 图片文件暂时不保存
  // if (type === 3) {
  //   // 优先使用 filePath，其次 thumb 下载
  //   if (filePath && fs.existsSync(filePath)) {
  //     formattedMsg.file = filePath
  //   } else if (thumb) {
  //     const saved = await saveMessageFile(agent, msg)
  //     if (saved) formattedMsg.file = saved
  //   }
  // }
  return formattedMsg
}

/**
 * 移除文本中的所有空格字符
 * @param {string} text 原始文本
 * @returns {string} 移除空格后的文本
 */
function removeWhitespace(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }
  // 移除所有空格字符（包括普通空格、不间断空格、制表符、换行符等）
  return text.replace(/\s+/g, '')
}

/**
 * 智能清理文本格式，避免换行符导致的数据拼接错误
 * @param {string} text 原始文本
 * @returns {string} 清理后的文本
 */
function cleanTextFormat(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }

  let cleanedText = text

  // 1. 处理数字+换行符+数字的情况，在换行符前后添加空格分隔
  // 匹配: 数字/小数点/价格符号 + 换行符 + 数字
  cleanedText = cleanedText.replace(/([0-9.￥元≤＞]+)\n([0-9])/g, '$1 $2')

  // 2. 处理公司名+换行符+数字的情况，保留换行符但确保格式正确
  // 匹配: 中文/英文 + 换行符 + 数字，在换行符后添加空格
  cleanedText = cleanedText.replace(/([^\n])\n([0-9])/g, '$1\n $2')

  // 3. 标准化多个换行符为单个换行符
  cleanedText = cleanedText.replace(/\n+/g, '\n')

  // 4. 标准化多个空格为单个空格
  cleanedText = cleanedText.replace(/[ \t]+/g, ' ')

  // 5. 移除行首行尾的空格
  cleanedText = cleanedText.replace(/^ +| +$/gm, '')

  return cleanedText.trim()
}

export {
  saveMessageFile,
  getContactName,
  getRoomName,
  formatMessage,
  createFileBox,
  removeWhitespace,
  removeEmojisForAI,
  cleanTextFormat,
}
