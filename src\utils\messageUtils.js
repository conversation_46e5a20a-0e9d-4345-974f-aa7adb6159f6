import fs from 'fs'
import path from 'path'
import config from '../config/index.js'
import { formatTimestamp } from './dateUtils.js'
import { ensureDirSync, fileExistsSync, createFileBox } from './fileUtils.js'
import { log } from './logging.js'

/**
 * 保存接收到的文件
 * @param {Object} agent WechatferryAgent实例
 * @param {Object} msg 消息对象
 * @param {string} saveDir 保存目录
 * @returns {Promise<string|null>} 保存的文件路径
 */
async function saveMessageFile(agent, msg, saveDir = config.paths.files) {
  try {
    if (!saveDir || typeof saveDir !== 'string') {
      throw new Error('无效的保存目录')
    }

    // 确保保存目录存在
    ensureDirSync(saveDir)

    if (!agent || typeof agent.downloadFile !== 'function') {
      throw new Error('无效的agent实例')
    }
    const fileBox = await agent.downloadFile(msg)
    const fileName = `${Date.now()}_${fileBox.name}`
    const savePath = path.join(saveDir, fileName)
    await fileBox.toFile(savePath)
    return savePath
  } catch (error) {
    log.c.error(`保存文件失败`, {
      error: error.message,
      stack: error.stack,
    })
    return null
  }
}

/**
 * 获取联系人名称
 * @param {Object} agent WechatferryAgent实例
 * @param {string} wxid 微信ID
 * @returns {string} 联系人名称
 */
function getContactName(agent, wxid) {
  try {
    if (!agent || typeof agent.getContactInfo !== 'function') {
      return wxid
    }
    const contact = agent.getContactInfo(wxid)
    return contact && contact.nickName ? contact.nickName : wxid
  } catch (error) {
    return wxid
  }
}

/**
 * 获取群聊名称
 * @param {Object} agent WechatferryAgent实例
 * @param {string} roomId 群聊ID
 * @returns {string} 群聊名称
 */
function getRoomName(agent, roomId) {
  try {
    if (!agent || typeof agent.getChatRoomInfo !== 'function') {
      return roomId
    }
    const room = agent.getChatRoomInfo(roomId)
    return room && room.nickName ? room.nickName : roomId
  } catch (error) {
    return roomId
  }
}

/**
 * 专门用于AI处理前清理表情符号和特殊字符的函数
 * 基于Unicode 16.0标准，覆盖所有已知的表情符号和特殊字符范围
 * @param {string} text 原始文本
 * @returns {string} 清理后的文本
 */
function removeEmojisForAI(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }

  // 构建一个综合的表情符号和特殊字符正则表达式
  // 基于Unicode标准的各个表情符号块
  const emojiAndSpecialCharsRegex = new RegExp(
    [
      // === 主要表情符号范围 ===
      '\\u{1F600}-\\u{1F64F}', // 表情符号 (Emoticons)
      '\\u{1F300}-\\u{1F5FF}', // 杂项符号和象形文字 (Miscellaneous Symbols and Pictographs)
      '\\u{1F680}-\\u{1F6FF}', // 交通和地图符号 (Transport and Map Symbols)
      '\\u{1F700}-\\u{1F77F}', // 炼金术符号 (Alchemical Symbols)
      '\\u{1F780}-\\u{1F7FF}', // 几何形状扩展 (Geometric Shapes Extended)
      '\\u{1F800}-\\u{1F8FF}', // 补充箭头-C (Supplemental Arrows-C)
      '\\u{1F900}-\\u{1F9FF}', // 补充符号和象形文字 (Supplemental Symbols and Pictographs)
      '\\u{1FA00}-\\u{1FA6F}', // 符号和象形文字扩展-A (Symbols and Pictographs Extended-A)
      '\\u{1FA70}-\\u{1FAFF}', // 符号和象形文字扩展-B (Symbols and Pictographs Extended-B)

      // === 新增的表情符号相关范围 ===
      '\\u{1F0A0}-\\u{1F0FF}', // 扑克牌符号 (Playing Cards)
      '\\u{1F100}-\\u{1F1FF}', // 封闭字母数字补充 (Enclosed Alphanumeric Supplement)
      '\\u{1F200}-\\u{1F2FF}', // 封闭CJK字母和月份 (Enclosed CJK Letters and Months)
      '\\u{1F650}-\\u{1F67F}', // 装饰符号 (Ornamental Dingbats)

      // === 符号和特殊字符范围 ===
      '\\u{2190}-\\u{21FF}', // 箭头 (Arrows)
      '\\u{2300}-\\u{23FF}', // 杂项技术符号 (Miscellaneous Technical)
      '\\u{2460}-\\u{24FF}', // 封闭字母数字 (Enclosed Alphanumerics)
      '\\u{25A0}-\\u{25FF}', // 几何形状 (Geometric Shapes)
      '\\u{2600}-\\u{26FF}', // 杂项符号 (Miscellaneous Symbols)
      '\\u{2700}-\\u{27BF}', // 装饰符号 (Dingbats)
      '\\u{2B00}-\\u{2BFF}', // 杂项符号和箭头 (Miscellaneous Symbols and Arrows)

      // === CJK相关符号 ===
      '\\u{3000}-\\u{303F}', // CJK符号和标点 (CJK Symbols and Punctuation)
      '\\u{3200}-\\u{32FF}', // 封闭CJK字母和月份 (Enclosed CJK Letters and Months)
      '\\u{3300}-\\u{33FF}', // CJK兼容性 (CJK Compatibility)

      // === 其他重要范围 ===
      '\\u{FE00}-\\u{FE0F}', // 变体选择器 (Variation Selectors)
      '\\u{1F1E0}-\\u{1F1FF}', // 区域指示符号/国旗 (Regional Indicator Symbols)

      // === 零宽字符和控制字符 ===
      '\\u200B-\\u200D', // 零宽字符 (Zero Width Characters)
      '\\uFEFF', // 字节顺序标记 (Byte Order Mark)
      '\\u2000-\\u206F', // 通用标点 (General Punctuation)
      '\\u2E00-\\u2E7F', // 补充标点 (Supplemental Punctuation)
    ].join('|'),
    'gu',
  )

  return (
    text
      // 移除所有表情符号和特殊字符
      .replace(emojiAndSpecialCharsRegex, '')
      // 清理控制字符（除了基本的换行符和制表符）
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // 清理可能导致问题的特殊字符
      .replace(/[\x00\x1A]/g, '')
      // 清理多余的空白字符，但保留基本的空格和换行
      .replace(/\s+/g, ' ')
      .trim()
  )
}

/**
 * 格式化消息对象
 * @param {Object} agent WechatferryAgent实例
 * @param {Object} msg 消息对象
 * @returns {Object} 格式化后的消息对象
 */
async function formatMessage(agent, msg) {
  const { id, type, is_group, content, sender, roomid, thumb, ts, filePath } = msg
  const contactName = getContactName(agent, sender)
  const formattedMsg = {
    messageId: id,
    msgType: type,
    content: removeEmojisForAI(content || ''),
    sender: contactName,
    senderId: sender,
    sendTime: formatTimestamp(ts, 'YYYY-MM-DD HH:mm:ss'),
    ...(is_group && {
      room: getRoomName(agent, roomid),
      roomId: roomid,
    }),
  }
  // 图片文件暂时不保存
  // if (type === 3) {
  //   // 优先使用 filePath，其次 thumb 下载
  //   if (filePath && fs.existsSync(filePath)) {
  //     formattedMsg.file = filePath
  //   } else if (thumb) {
  //     const saved = await saveMessageFile(agent, msg)
  //     if (saved) formattedMsg.file = saved
  //   }
  // }
  return formattedMsg
}

/**
 * 移除文本中的所有空格字符
 * @param {string} text 原始文本
 * @returns {string} 移除空格后的文本
 */
function removeWhitespace(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }
  // 移除所有空格字符（包括普通空格、不间断空格、制表符、换行符等）
  return text.replace(/\s+/g, '')
}

/**
 * 智能清理文本格式，避免换行符导致的数据拼接错误
 * @param {string} text 原始文本
 * @returns {string} 清理后的文本
 */
function cleanTextFormat(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }

  let cleanedText = text

  // 1. 处理数字+换行符+数字的情况，在换行符前后添加空格分隔
  // 匹配: 数字/小数点/价格符号 + 换行符 + 数字
  cleanedText = cleanedText.replace(/([0-9.￥元≤＞]+)\n([0-9])/g, '$1 $2')

  // 2. 处理公司名+换行符+数字的情况，保留换行符但确保格式正确
  // 匹配: 中文/英文 + 换行符 + 数字，在换行符后添加空格
  cleanedText = cleanedText.replace(/([^\n])\n([0-9])/g, '$1\n $2')

  // 3. 标准化多个换行符为单个换行符
  cleanedText = cleanedText.replace(/\n+/g, '\n')

  // 4. 标准化多个空格为单个空格
  cleanedText = cleanedText.replace(/[ \t]+/g, ' ')

  // 5. 移除行首行尾的空格
  cleanedText = cleanedText.replace(/^ +| +$/gm, '')

  return cleanedText.trim()
}

export {
  saveMessageFile,
  getContactName,
  getRoomName,
  formatMessage,
  createFileBox,
  removeWhitespace,
  removeEmojisForAI,
  cleanTextFormat,
}
